# 修改项目的提示词

## 1. 
你先看下这个项目，我要如何运行这个项目，让我的微信接入AI功能，自动回复
1.1 🔥 wcf (推荐) - 基于wcferry的微信个人号接入
    - 安装wcferry依赖
    - 修改配置文件以使用wcf方式
    -  配置触发前缀和群聊设置
让我们优化一下配置，让微信更好地工作：

1.2 
我想用这个ai和模型：{{{
硅基流动：
接口：
https://api.siliconflow.cn/v1
api key：
sk-ptebmgpiesctnbdmeewgpboobiruklatpuaexzoshxcfejaf
模型：
Pro/deepseek-ai/DeepSeek-V3

}}}

## 2. 
你看下我之前的配置， 之前只卖 cursor登陆助手，现在增加了 augment登陆助手。 这两个是我之前另一个AI客服的 提示词  你先看下 @d:\GitHub\weixin-bot\chatgpt-on-wechat-master/之前的项目文档\config-之前的配置，包含提示词.yaml ，然后再看下我目前卖的 cursor和 augment登陆助手的教程文档  @d:\GitHub\weixin-bot\chatgpt-on-wechat-master/之前的项目文档\1. augment （号池）登陆助手教程.md @d:\GitHub\weixin-bot\chatgpt-on-wechat-master/之前的项目文档\2. cursor登陆助手的完整 售后文档-7.29.md .结合当前的项目，分析如何修改这个项目，能当我的 产品的AI客服。先给出一个方案。争取最小的修改，比如修改 prompt，或者 知识库文件。达到我的目的。

### 🎯 最优方案：修改AI助手的人格描述（character_desc
这是最简单、最直接的方案，只需要修改一个配置项就能实现您的需求。

## 3.

能将这两个文档，做成 知识库文档，每次AI回答的时候，根据相关的资料来回答 。你分析下，如何最小代码实现这个方案 @d:\GitHub\weixin-bot\chatgpt-on-wechat-master/之前的项目文档\1. augment （号池）登陆助手教程.md @d:\GitHub\weixin-bot\chatgpt-on-wechat-master/之前的项目文档\2. cursor登陆助手的完整 售后文档-7.29.md

### 🎯 最优方案：基于keyword插件实现知识库
📋 方案分析
为什么选择keyword插件：

✅ 零代码修改 - 只需配置文件
✅ 现成插件 - 项目已内置
✅ 精确匹配 - 关键词触发，响应准确
✅ 支持富媒体 - 可返回文本、图片、文件等
✅ 高优先级 - desire_priority=900，优先处理


## 4. 
我的项目之后要如何启动和修改增补知识。我是小白，给我一个新手文档。然后增补到 @d:\GitHub\weixin-bot\chatgpt-on-wechat-master/README.md 

## 5.
将我下面的具体的教程链接 增补到 知识库中，在用户需要的时候，要能直接回复链接。
1. augment （号池）登陆助手教程：https://w1yklj2r7gv.feishu.cn/wiki/WiYNwLCnti7kXmkDj5Zcl3hGnMb  ；
2. cursor登陆助手的完整 售后文档-7.29 ：https://w1yklj2r7gv.feishu.cn/wiki/RWz3wokbdihMsXktCyBcx7oCnsg  ;
我的淘宝购买链接：
【Augment Code 号池-登陆助手】
购买渠道 (淘宝自动发货): https://item.taobao.com/item.htm?ft=t&id=953114980777  ；
cursor登陆助手：购买渠道 (淘宝自动发货):
https://item.taobao.com/item.htm?id=931668414172

### 🔧 解决方案
让我修改AI的人格描述，让它更直接地提供链接：


## 6.
你根据这个人设来修改下当前的客服人设，分析怎么修改下：{{{
   你现在是"Cursor登录小助手"，一位超级有耐心、超级懂你的官方客服小姐姐/小哥哥！你的任务是：
    1. 用轻松、活泼、友好的方式和用户聊天，就像和好朋友沟通一样自然。可以适当用一些年轻人喜欢的表达，比如 "亲~", "搞定！", "小case~", "没问题哒~", "妥妥的~"。
    2. 当用户提问时，先快速给出一个简单明了的核心答案，让用户一下子就明白怎么回事。
    3. 然后，一定要引导用户去看《Cursor登录助手操作说明》文档里更详细的步骤或解释，告诉他们具体是哪个章节和小节（使用原文中【】括号里的章节名称）。比如："想了解更多细节，可以看看操作说明文档里【常见问题错误对照 - 第X节：具体问题描述】那部分哦~ 那里有图文并茂的教程！"
    4. 回答要精准有效，直击痛点，别说太多无关的话。
    5.AI客服回答要像真人一样回复，没有 markdown的（*）、（#） 等符号，微信消息中不能正确渲染。
}}}

