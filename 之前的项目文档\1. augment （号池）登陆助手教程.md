# Augment续杯插件

| 套餐 (有效期) | 可获取帐号上限数 | 总实用额度 |
| :--- | :--- | :--- |
| 1天 | 5个 | 每个pro帐号300额度 |
| 7天 | 20个 | 每个帐号约14天内有效,请在有需要时再获取帐号 |
| 30天 | 50个 | (帐号是提前注册,但不影响使用) |
| 30天 | 100个 | 300*帐号数 = 总额度 |

套餐定价:
·   3.9元 (日卡/5个号)
·   11.9元 (半月卡/20个号)
·  19.9元 (月卡/50个号)
·  29.9元 (月卡/100个号) 推荐



有效期为插件使用有效期,有效期内可以一键获取帐号登录,过期就过期,剩余帐号将无法获取,这点请熟知。

不绑定电脑设备,不限设备,可多设备同时使用,插件过期或帐号获取完即止

https://m.tb.cn/h.hkFgwJG?tk=6u8k43UEwL9 【闲鱼购买,自动发货】

**(非常重要)请加微信售后群,有问题发图,反馈**

---

![群聊二维码](https://i.imgur.com/8Q9g2f2.png)

**群聊：Augment续杯插件 100 售后群**

该二维码7天内(8月4日前)有效,重新进入将更新

---

# VS编辑器系列

## Visual Studio/Cursor/Windsurf 号池扩展下载

**(只需要安装这个插件就可以, augment 请自行官方下载或插件市场下载)**

- **codepool-1.0.20.vsix** (1.31MB)

如果在 Remote SSH 中使用,需要在SSH中也安装号池插件

[Visual Studio插件安装和使用说明](about:blank)
[在Cursor/Windsurf使用说明](about:blank)

---

# IDEA编辑器系列

## IDEA/PyCharm/Goland/Android Studio等JB系列

老版本在插件市场自行下载 augment 插件并安装 (如果市场找不到代表aug不支持该版本,请升级IEDA)

**2024.3以上版本下载下面插件(aug+号池插件)**

**1.1 IDEA augment插件下载 (必须下载下面的,不然容易封号)**

- **intellij-augment-0.249.2.zip** (57.61MB)
- **intellij-augment-0.240.1.zip** (57.29MB)
- **intellij-augment-0.221.1.zip** (56.81MB)
- **intellij-augment-0.212.2.zip** (55.48MB)

**1.2 IDEA号池插件下载**

- **augment-token-manager-1.0.2.zip** (299.43KB)

[IDEA插件安装和使用说明](about:blank)

---

## Visual Studio 安装

打开扩展,将 codepool-x..vsix 文件拖拽到 侧边栏扩展页,即可完成安装

![VS Code 插件安装](https://i.imgur.com/M6x2b2g.png)
*打开扩展,将codepool-x.x.xx.vsix 文件拖拽到插件侧边栏*

![VS Code 插件安装后](https://i.imgur.com/yF9W3yB.png)
*安装后,点击底部状态栏上的号池图标,打开插件*

---

**请严格遵守切号流程:**
只保留一个IDE(窗口),点击切换新帐号,新建个对话,(必须新建个对话,不然秒封)

![切换账号流程1](https://i.imgur.com/uG9i05h.png)
*输入激活码,点击登录,点击“切换新帐号”即可*

![切换账号流程2](https://i.imgur.com/a5z2pS8.png)
*切换账号后提示*

---

## IDEA/PyCharm/Goland/Android Studio 等JB系列安装

打开设置 - 插件 - 从磁盘安装插件

下载的augment 跟 augment-token-manager 不用解压

安装augment 跟 augment-token-manager号池插件,安装后重启即可

![IDEA 插件安装](https://i.imgur.com/R5C6fFm.png)
*IDEA 插件安装步骤*

---

# IDEA 号池插件使用方法

**1. 右下角点击号池-打开插件 (如果插件安装成功后,底部还没有,重启下IDE编辑器)**
如果底部没有,菜单工具里面也有的(看图2)

![IDEA 插件位置1](https://i.imgur.com/6n4lO2F.png)
*1. 底部状态栏*

![IDEA 插件位置2](https://i.imgur.com/Hl5a8yK.png)
*2. 菜单工具*

**2. 打开插件输入激活码,登录号池**

**切号流程:**
只保留一个IDE,点击切换新帐号,新建个对话,(必须新建个对话,不然秒封)

![IDEA 切换账号](https://i.imgur.com/w9N0oV3.png)
*号池管理界面*

---

# 各种其它常见问题:

**怎么只有 Chat 没 Agent ?**
右上角 New - 选择Agent即可

![选择 Agent](https://i.imgur.com/2sA8x8E.png)
*在 New 中选择 Agent*

**如何移动到右侧**
看图示例,或者按住后拖过去也可以

![移动插件位置](https://i.imgur.com/8Fk7u7f.png)
*移动 Augment 插件到侧边栏*

---

**Your account ****com has been suspended. To continue, purchase asubscription.**

提示Your account 表示号被封或额度用完了,需要重新获取新号

只保留一个IDE(窗口),点击切换新帐号,新建对话(右上角点击New新建对话,必须新建对话,不然秒封)

![账号挂起提示](https://i.imgur.com/o1C0g4x.png)
*账号被暂停的提示信息*

---

**The selected text exceeds ..... reduce the amount of text and try again**

**提示超出文本限制**

别选中太多文本,选中文本对话会一起发出的,如果太长就会提示的(看示例图)

取消文本选中就可以 - 或者 - 输入框取消Selection

![文本超长提示](https://i.imgur.com/4a0o8oD.png)
*选中文本过长时的提示*

---

## 另外挂魔法可以避免以下这些问题

魔法推荐 跟 设置参考: https://github.com/a3322770/limit/blob/main/Augment.md

**1. Generating response...**

最近每天16点-17点 官方可能存在高负载情况,高负载会导致响应慢,这个是正常的

如果在非高峰期时间段如果出现响应慢,经测试... 一般挂魔法就可以解决

![Generating response](https://i.imgur.com/xT4y2n8.png)
*Generating response... 提示*

**2. Loading Monaco Editor...**

打开aug一直显示加载中....

经测试... 网络跟aug之间连接的问题,一般挂魔法就可以。

![Loading Monaco Editor](https://i.imgur.com/r6eB9zE.png)
*Loading Monaco Editor... 提示*

**3. We encountered an issue sending your message.Please try again**

主要跟个人环境有关,如:网络,上下文太多了,或官方问题,一般点击 try again 重试就可以了

经测试... 一般挂魔法不容易出现We encountered an... 会比较稳

![发送消息失败](https://i.imgur.com/tV8o3lE.png)
*发送消息遇到问题的提示*

**4. Terminated Request ID:*******

aug出错导致的终止,输入框继续发送信息就可以

经测试...一般挂魔法会比较稳,不容易出错

![请求终止](https://i.imgur.com/n6t5M6B.png)
*请求被终止的提示*

**5. Augment 飘红**

经测试... 网络跟aug之间连接的问题,一般挂魔法就可以

![Augment 飘红](https.imgur.com/p8vF4fK.png)
*Augment 插件图标飘红*

---

## 关于Augment使用什么模型(看看官方公告)

https://support.augmentcode.com/articles/8712480908-is-my-augment-code-instance-using-claude-sonnet-4

## Cursor/Windsurf 扩展市场找不到augment插件?

1. Cursor/Windsurf屏蔽了aug,在cursor扩展中心是找不到的
2. 需要自己在Visual Studio编辑器将aug下载插件后,拖到扩展安装就可以
3. 示例插件,其它版本,自行在vs更新后下载(不定时更新)

- **augment.vscode-augment-0.502.1.vsix** (11.74MB)
- **augment.vscode-augment-0.492.2.vsix** (5.92MB)

![在 VS Code 中安装 Augment](https://i.imgur.com/t3c7E3a.png)
*在 Visual Studio Code 中手动安装 Augment 插件*